<!DOCTYPE html>
<!--
This is a starter template page. Use this page to start your new project from
scratch. This page gets rid of all links and provides the needed markup only.
-->
<html lang="en">

<?php $__env->startSection('htmlheader'); ?>
    <?php echo $__env->make('adminlte::super_layout.partials.htmlheader', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>
<?php echo $__env->yieldSection(); ?>

<!--
BODY TAG OPTIONS:
=================
Apply one or more of the following classes to get the
desired effect
|---------------------------------------------------------|
| SKINS         | skin-blue                               |
|               | skin-black                              |
|               | skin-purple                             |
|               | skin-yellow                             |
|               | skin-red                                |
|               | skin-green                              |
|---------------------------------------------------------|
|LAYOUT OPTIONS | fixed                                   |
|               | layout-boxed                            |
|               | layout-top-nav                          |
|               | sidebar-collapse                        |
|               | sidebar-mini                            |
|---------------------------------------------------------|
-->
<body class="skin-black-light  sidebar-mini">
<div id="app">
    <div class="wrapper">

    <?php echo $__env->make('adminlte::super_layout.partials.mainheader', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>

    <?php echo $__env->make('adminlte::super_layout.partials.sidebar', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">


        <?php echo $__env->yieldContent('header'); ?>
        <!-- Main content -->
        <section class="content">
            <!-- Your Page Content Here -->
            <?php echo $__env->yieldContent('main-content'); ?>
        </section><!-- /.content -->
    </div><!-- /.content-wrapper -->

    <?php echo $__env->make('adminlte::super_layout.partials.controlsidebar', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>

    <?php echo $__env->make('adminlte::super_layout.partials.footer', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>

</div><!-- ./wrapper -->
</div>
<?php $__env->startSection('scripts'); ?>
    <?php echo $__env->make('adminlte::super_layout.partials.scripts', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>
<?php echo $__env->yieldSection(); ?>
<script src="<?php echo e(asset('/plugins/select2/select2.js')); ?>" type="text/javascript"></script>
<?php echo $__env->yieldContent('footer-script'); ?>
    <script>

    $('.select2').select2();
    // Enable pusher logging - don't include this in production
    //Pusher.logToConsole = true;

    var pusher = new Pusher('a07b0f4928ae83a12227', {
      cluster: 'ap1',
      forceTLS: true
    });

    var channel = pusher.subscribe('loading-channel');
    channel.bind('loading-notification', function(data) {
      if(JSON.stringify(data.notification) != null){
          alertmessage = data.notification;
          alertmessage = alertmessage.replace(/"/g, "'");
          toastr.warning(alertmessage,'Message!');
      }
    });

    // Scheduling Notifications
    $(document).ready(function() {
        // Load notifications on page load
        loadNotifications();

        // Refresh notifications every 60 seconds
        setInterval(loadNotifications, 60000);

        // Mark notification as read when clicked
        $(document).on('click', '.notification-item', function() {
            var notificationId = $(this).data('id');
            markAsRead(notificationId);
        });

        // Mark all notifications as read
        $('#mark-all-read').click(function(e) {
            e.preventDefault();
            markAllAsRead();
        });

        // Function to load notifications
        function loadNotifications() {
            $.ajax({
                url: '/notifications',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    updateNotificationBadge(response.unread_count);
                    updateNotificationMenu(response.notifications);
                }
            });
        }

        // Function to mark notification as read
        function markAsRead(notificationId) {
            $.ajax({
                url: '/notifications/mark-as-read',
                type: 'POST',
                data: {
                    notification_id: notificationId,
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        loadNotifications();
                    }
                }
            });
        }

        // Function to mark all notifications as read
        function markAllAsRead() {
            $.ajax({
                url: '/notifications/mark-all-as-read',
                type: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        loadNotifications();
                    }
                }
            });
        }

        // Function to update notification badge
        function updateNotificationBadge(count) {
            var badge = $('.notification-badge');
            if (count > 0) {
                badge.text(count).show();
            } else {
                badge.hide();
            }
        }

        // Function to update notification menu
        function updateNotificationMenu(notifications) {
            var menu = $('.notification-menu');
            var header = $('.notification-header');

            // Update header
            if (notifications.length > 0) {
                header.text('You have ' + notifications.length + ' scheduling notifications');
            } else {
                header.text('You have no scheduling notifications');
            }

            // Clear menu
            menu.empty();

            // Add notifications to menu
            if (notifications.length > 0) {
                $.each(notifications, function(index, notification) {
                    var icon = 'fa-calendar';
                    var color = 'text-aqua';

                    if (notification.type === 'conflict') {
                        icon = 'fa-exclamation-triangle';
                        color = 'text-red';
                    }

                    var item = $('<li class="notification-item" data-id="' + notification.id + '">' +
                                '<a href="#">' +
                                '<i class="fa ' + icon + ' ' + color + '"></i> ' +
                                notification.title + '<br>' +
                                '<small>' + notification.message + '</small>' +
                                '</a>' +
                                '</li>');

                    if (notification.is_read == 0) {
                        item.addClass('unread');
                    }

                    menu.append(item);
                });
            } else {
                menu.append('<li><a href="#"><i class="fa fa-check text-green"></i> No notifications</a></li>');
            }
        }
    });
  </script>

  <style>
    .notification-item.unread {
        background-color: #f9f9f9;
    }
    .notification-item.unread a {
        font-weight: bold;
    }
    .notification-item small {
        display: block;
        color: #999;
        margin-top: 5px;
    }
  </style>

</body>
</html>
