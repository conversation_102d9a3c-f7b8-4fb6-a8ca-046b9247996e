<?php
$layout = "";

if(Auth::user()->is_first_login == 1){
    $layout = 'layouts.first_login';
}else{
    if(Auth::user()->accesslevel == 100){
        $layout = 'layouts.superadmin';
    }elseif(Auth::user()->accesslevel == 50){
        $layout = 'layouts.collegeadmin';
    }elseif(Auth::user()->accesslevel == 1){
        $layout = 'layouts.instructor';
    }elseif(Auth::user()->accesslevel == 0){
        $layout = 'layouts.admin';
    }
}
?>

@extends($layout)

@section('main-content')
<section class="content-header">
    <h1><i class="fa fa-calendar"></i>
        Faculty Schedule
        <small>{{ $instructorUser->name }} {{ $instructorUser->lastname }}</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="{{url('/')}}"><i class="fa fa-home"></i> Home</a></li>
        <li><a href="{{ route('collegeadmin.faculty_loading.index') }}">Faculty Loading</a></li>
        <li class="active">Faculty Schedule</li>
    </ol>
</section>

<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title">{{ $instructorUser->name }} {{ $instructorUser->middlename }} {{ $instructorUser->lastname }}</h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-primary btn-sm load-courses" data-instructor="{{ $instructor }}">
                            <i class="fa fa-plus"></i> Add Course
                        </button>
                        <button type="button" class="btn btn-success btn-sm" id="print-schedule">
                            <i class="fa fa-print"></i> Print Schedule
                        </button>
                    </div>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 150px;">ID</th>
                                    <td>{{ $instructorUser->username ?? $instructorUser->id }}</td>
                                </tr>
                                <tr>
                                    <th>Name</th>
                                    <td>{{ strtoupper($instructorUser->lastname) }}, {{ strtoupper($instructorUser->name) }} {{ strtoupper($instructorUser->middlename) }}</td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td>{{ $instructorUser->email }}</td>
                                </tr>
                                <tr>
                                    <th>Faculty Status</th>
                                    <td>{{ $info ? $info->employee_type : 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Department</th>
                                    <td>{{ $info ? $info->department : 'N/A' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if(Session::has('success'))
                        <div class="alert alert-success">
                            {{ Session::get('success') }}
                        </div>
                    @endif

                    @if(Session::has('error'))
                        <div class="alert alert-danger">
                            {{ Session::get('error') }}
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-8">
                            <div id="calendar"></div>
                        </div>
                        <div class="col-md-4">
                            <div class="box box-info">
                                <div class="box-header with-border">
                                    <h3 class="box-title">Current Teaching Load</h3>
                                </div>
                                <div class="box-body">
                                    @if(count($schedules) > 0)
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Course</th>
                                                        <th>Schedule</th>
                                                        <th>Room</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($schedules as $schedule)
                                                        <tr>
                                                            <td>{{ $schedule->course_code }}</td>
                                                            <td>
                                                                {{ $schedule->sched_day }}
                                                                {{ date('h:i A', strtotime($schedule->sched_from)) }} -
                                                                {{ date('h:i A', strtotime($schedule->sched_to)) }}
                                                            </td>
                                                            <td>{{ $schedule->room }}</td>
                                                            <td>
                                                                <button type="button" class="btn btn-danger btn-xs remove-load" data-schedule="{{ $schedule->id }}">
                                                                    <i class="fa fa-trash"></i>
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <div class="alert alert-info">
                                            No teaching load assigned yet.
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="box-footer">
                    <a href="{{ route('collegeadmin.faculty_loading.index') }}" class="btn btn-default">Back to List</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for adding courses -->
    <div class="modal fade" id="addCourseModal" tabindex="-1" role="dialog" aria-labelledby="addCourseModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="addCourseModalLabel">Add Course to Faculty Load</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="search-course">Search Course</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="search-course" placeholder="Enter course code or name">
                                    <span class="input-group-btn">
                                        <button class="btn btn-primary" type="button" id="search-course-btn">Search</button>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="available-courses">
                        <p>Search for courses to add to faculty load.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('footer-script')
<link rel="stylesheet" href="{{ asset('plugins/fullcalendar/fullcalendar.min.css') }}">
<script src="{{ asset('plugins/fullcalendar/moment.min.js') }}"></script>
<script src="{{ asset('plugins/fullcalendar/fullcalendar.min.js') }}"></script>
<script src="{{ asset('plugins/jQueryUI/jquery-ui.min.js') }}"></script>
<style>
    .draggable-schedule {
        cursor: move;
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 3px;
    }
    .callout {
        border-left: 5px solid #eee;
        border-radius: 3px;
    }
    .callout-info {
        border-left-color: #5bc0de;
        background-color: #f4f8fa;
    }
    .callout-warning {
        border-left-color: #f0ad4e;
        background-color: #fcf8f2;
    }
    .callout-success {
        border-left-color: #5cb85c;
        background-color: #f3f8f3;
    }
    .callout-danger {
        border-left-color: #d9534f;
        background-color: #fdf7f7;
    }
    .fc-event {
        cursor: pointer;
    }
    .ui-draggable-dragging {
        z-index: 9999 !important;
    }
</style>
<script>
$(function() {
    // Initialize calendar with droppable functionality
    $('#calendar').fullCalendar({
        header: {
            left: 'prev,next today',
            center: 'title',
            right: 'month,agendaWeek,agendaDay'
        },
        defaultView: 'agendaWeek',
        minTime: '07:00:00',
        maxTime: '22:00:00',
        hiddenDays: [0], // Hide Sunday
        firstDay: 1, // Monday as first day
        height: 500,
        allDaySlot: false,
        columnFormat: 'ddd',
        editable: false,
        droppable: true, // Allow external events to be dropped onto the calendar
        drop: function(date, jsEvent, ui, resourceId) {
            // Called when a draggable item is dropped onto the calendar
            var $item = $(ui.helper);
            var eventData = $item.data('event');
            var offeringId = eventData.offering;
            var instructorId = '{{ $instructor }}';

            // Add the course to faculty load via AJAX
            $.ajax({
                url: "{{ url('/ajax/collegeadmin/faculty_loading/add_faculty_load') }}",
                type: 'GET',
                data: {
                    instructor_id: instructorId,
                    offering_id: offeringId
                },
                success: function(data) {
                    if (data.success) {
                        alert('Course added to faculty load successfully!');
                        location.reload(); // Reload the page to update the schedule
                    } else {
                        alert('Error: ' + data.message);
                    }
                },
                error: function() {
                    alert('Error adding course to faculty load.');
                }
            });
        },
        events: [
            @foreach($schedules as $schedule)
                @php
                    // Map day letters to day numbers
                    $dayMap = [
                        'M' => 1, // Monday
                        'T' => 2, // Tuesday
                        'W' => 3, // Wednesday
                        'H' => 4, // Thursday
                        'F' => 5, // Friday
                        'S' => 6, // Saturday
                    ];

                    // Parse schedule days
                    $days = str_split($schedule->sched_day);
                @endphp

                @foreach($days as $day)
                    @if(isset($dayMap[$day]))
                        {
                            id: '{{ $schedule->id }}',
                            title: '{{ $schedule->course_code }} ({{ $schedule->room }})',
                            start: '{{ date("Y-m-d", strtotime("this week +" . ($dayMap[$day] - 1) . " days")) }}T{{ $schedule->sched_from }}',
                            end: '{{ date("Y-m-d", strtotime("this week +" . ($dayMap[$day] - 1) . " days")) }}T{{ $schedule->sched_to }}',
                            backgroundColor: '#3c8dbc',
                            borderColor: '#3c8dbc',
                        },
                    @endif
                @endforeach
            @endforeach
        ],
        eventClick: function(calEvent, jsEvent, view) {
            // Handle click on calendar event (for removing)
            if (confirm('Do you want to remove this course from the faculty load?')) {
                var scheduleId = calEvent.id;

                $.ajax({
                    url: "{{ url('/ajax/collegeadmin/faculty_loading/remove_faculty_load') }}",
                    type: 'GET',
                    data: { schedule_id: scheduleId },
                    success: function(data) {
                        if (data.success) {
                            alert('Course removed from faculty load successfully!');
                            location.reload(); // Reload the page to update the schedule
                        } else {
                            alert('Error: ' + data.message);
                        }
                    },
                    error: function() {
                        alert('Error removing course from faculty load.');
                    }
                });
            }
        }
    });

    // Initialize draggable functionality for search results
    function initDraggableItems() {
        $('.draggable-schedule').each(function() {
            // Initialize draggable
            $(this).draggable({
                zIndex: 999,
                revert: true,
                revertDuration: 0,
                helper: 'clone',
                appendTo: 'body',
                scroll: false,
                start: function(event, ui) {
                    $(this).css('z-index', 1000);
                }
            });

            // Store event data
            var eventObject = JSON.parse($(this).attr('data-event'));
            $(this).data('event', eventObject);
        });
    }

    // Handle click on Add Course button
    $('.load-courses').click(function() {
        var instructorId = $(this).data('instructor');
        $('#addCourseModal').data('instructor', instructorId);
        $('#addCourseModal').modal('show');
    });

    // Handle search button click
    $('#search-course-btn').click(function() {
        var searchTerm = $('#search-course').val();
        var instructorId = $('#addCourseModal').data('instructor');

        if (searchTerm.length < 2) {
            alert('Please enter at least 2 characters to search.');
            return;
        }

        $.ajax({
            url: "{{ url('/ajax/collegeadmin/faculty_loading/search_courses') }}",
            type: 'GET',
            data: {
                search: searchTerm,
                instructor_id: instructorId
            },
            success: function(data) {
                $('#available-courses').html(data);
                // Initialize draggable items after loading search results
                initDraggableItems();
            },
            error: function() {
                $('#available-courses').html('<p class="text-danger">Error searching for courses.</p>');
            }
        });
    });

    // Handle Enter key in search box
    $('#search-course').keypress(function(e) {
        if (e.which == 13) {
            $('#search-course-btn').click();
            return false;
        }
    });

    // Handle add course to faculty load (delegated event)
    $(document).on('click', '.add-to-load', function() {
        var instructorId = $('#addCourseModal').data('instructor');
        var offeringId = $(this).data('offering');

        $.ajax({
            url: "{{ url('/ajax/collegeadmin/faculty_loading/add_faculty_load') }}",
            type: 'GET',
            data: {
                instructor_id: instructorId,
                offering_id: offeringId
            },
            success: function(data) {
                if (data.success) {
                    alert('Course added to faculty load successfully!');
                    location.reload(); // Reload the page to update the schedule
                } else {
                    alert('Error: ' + data.message);
                }
            },
            error: function() {
                alert('Error adding course to faculty load.');
            }
        });
    });

    // Handle remove course from faculty load
    $('.remove-load').click(function() {
        if (confirm('Are you sure you want to remove this course from the faculty load?')) {
            var scheduleId = $(this).data('schedule');

            $.ajax({
                url: "{{ url('/ajax/collegeadmin/faculty_loading/remove_faculty_load') }}",
                type: 'GET',
                data: { schedule_id: scheduleId },
                success: function(data) {
                    if (data.success) {
                        alert('Course removed from faculty load successfully!');
                        location.reload(); // Reload the page to update the schedule
                    } else {
                        alert('Error: ' + data.message);
                    }
                },
                error: function() {
                    alert('Error removing course from faculty load.');
                }
            });
        }
    });

    // Handle print schedule
    $('#print-schedule').click(function() {
        window.print();
    });
});
</script>
@endsection
