<?php

namespace App\Http\Controllers\CollegeAdmin\Ajax;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use App\User;
use App\room_schedules;
use App\curriculum;
use Illuminate\Support\Facades\DB;

class FacultyLoadingAjax extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('checkIfActivated');
        $this->middleware('collegeadmin');
    }

    /**
     * Get courses available for loading
     */
    public function coursesToLoad(Request $request)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get instructor ID and level from request
        $instructor = $request->input('instructor');
        $level = $request->input('level');

        // Get courses that are not assigned to any instructor
        $courses = room_schedules::distinct()
            ->where('is_active', 1)
            ->whereNull('instructor')
            ->get(['offering_id']);

        return view('collegeadmin.faculty_loading.ajax.courses_to_load', compact('level', 'courses'));
    }

    /**
     * Original courses to load method (for backward compatibility)
     */
    public function coursesToLoadOriginal(Request $request)
    {
        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get instructor ID from request
        $instructorId = $request->input('instructor_id');

        // Get offerings that are not yet assigned to any instructor
        $offerings = \App\offerings_infos_table::where('is_active', 1)
            ->whereNotIn('id', function ($query) {
                $query->select('offering_id')
                    ->from('room_schedules')
                    ->where('is_active', 1)
                    ->whereNotNull('instructor');
            })
            ->with([
                'curriculum' => function ($query) use ($collegeCode) {
                    $query->where('college_code', $collegeCode);
                }
            ])
            ->get()
            ->filter(function ($offering) {
                return $offering->curriculum !== null;
            });

        // Add course_code and course_name to each offering
        foreach ($offerings as $offering) {
            $curriculum = $offering->curriculum;
            $offering->course_code = $curriculum->course_code ?: 'Unknown';
            $offering->course_name = $curriculum->course_name ?: 'Unknown';

            // If course_name is empty or generic, try to get from subject relationship
            if (
                empty($curriculum->course_name) || $curriculum->course_name == 'Unknown' ||
                strpos($curriculum->course_name, 'Course:') === 0
            ) {
                $subject = $curriculum->subject;
                if ($subject) {
                    $offering->course_code = $subject->subject_code;
                    $offering->course_name = $subject->subject_name;
                }
            }
        }

        return view('collegeadmin.ajax.courses_to_load', compact('offerings', 'instructorId'));
    }

    /**
     * Get current load for an instructor
     */
    public function currentLoad(Request $request)
    {
        // Get instructor ID and level from request
        $instructor = $request->input('instructor');
        $level = $request->input('level');
        $countOnly = $request->input('count_only', false);

        // Get loads for the instructor
        $loads = DB::table('curricula')
            ->join('offerings_infos', 'curricula.id', 'offerings_infos.curriculum_id')
            ->join('room_schedules', 'room_schedules.offering_id', 'offerings_infos.id')
            ->where('room_schedules.instructor', $instructor)
            ->get();

        // Get tabular schedules
        $tabular_schedules = room_schedules::distinct()
            ->where('is_active', 1)
            ->where('instructor', $instructor)
            ->get(['offering_id']);

        // Get schedules
        $schedules = room_schedules::where('is_active', 1)
            ->where('instructor', $instructor)
            ->get();

        if ($countOnly) {
            return count($schedules);
        }

        return view('collegeadmin.faculty_loading.ajax.current_load', compact('schedules', 'instructor', 'level', 'tabular_schedules', 'loads'));
    }

    /**
     * Original current load method (for backward compatibility)
     */
    public function currentLoadOriginal(Request $request)
    {
        // Get instructor ID from request
        $instructorId = $request->input('instructor_id');
        $countOnly = $request->input('count_only', false);

        // Get schedules for this instructor
        $schedules = room_schedules::where('instructor', $instructorId)
            ->where('is_active', 1)
            ->get();

        if ($countOnly) {
            return count($schedules);
        }

        return view('collegeadmin.ajax.current_load', compact('schedules'));
    }

    /**
     * Add faculty load
     */
    public function addFacultyLoad(Request $request)
    {
        // Get instructor ID and offering ID from request
        $instructor = $request->input('instructor');
        $offering_id = $request->input('offering_id');

        // Check if instructor exists
        $info = \App\instructors_infos::where('instructor_id', $instructor)->first();
        if (!$info) {
            return response()->json([
                'success' => false,
                'message' => 'Instructor not found.'
            ]);
        }

        // Check if instructor has reached maximum units
        $loads = DB::table('curricula')
            ->join('offerings_infos', 'curricula.id', 'offerings_infos.curriculum_id')
            ->join('room_schedules', 'room_schedules.offering_id', 'offerings_infos.id')
            ->where('room_schedules.instructor', $instructor)
            ->get();

        $load_units = \App\UnitsLoad::where('instructor_id', $instructor)->get();

        if ($loads->sum('units') >= $load_units->sum('units')) {
            return response()->json([
                'success' => false,
                'message' => 'Maximum units exceeded.'
            ], 404);
        }

        // Check for schedule conflicts
        $schedules = room_schedules::where('offering_id', $offering_id)->get();

        foreach ($schedules as $schedule) {
            $conflicts = room_schedules::where('instructor', $instructor)
                ->where('day', $schedule->day)
                ->where(function ($query) use ($schedule) {
                    $query->whereBetween('time_starts', [$schedule->time_starts, $schedule->time_end])
                        ->orWhereBetween('time_end', [$schedule->time_starts, $schedule->time_end])
                        ->orWhere(function ($q) use ($schedule) {
                            $q->where('time_starts', '<=', $schedule->time_starts)
                                ->where('time_end', '>=', $schedule->time_end);
                        });
                })
                ->get();

            if ($conflicts->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Schedule conflict detected.'
                ], 500);
            }
        }

        // Assign the instructor to the schedules
        foreach ($schedules as $schedule) {
            $schedule->instructor = $instructor;
            $schedule->is_loaded = 1;
            $schedule->save();
        }

        return response()->json([
            'success' => true,
            'message' => 'Faculty load added successfully.'
        ]);
    }

    /**
     * Original add faculty load method (for backward compatibility)
     */
    public function addFacultyLoadOriginal(Request $request)
    {
        // Get instructor ID and offering ID from request
        $instructorId = $request->input('instructor_id');
        $offeringId = $request->input('offering_id');

        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Verify the instructor belongs to this college
        $instructor = User::where('id', $instructorId)
            ->where('accesslevel', 1)
            ->where(function ($query) use ($collegeCode) {
                $query->where('college_code', $collegeCode)
                    ->orWhereHas('instructorInfo', function ($q) use ($collegeCode) {
                        $q->where('college', $collegeCode);
                    });
            })
            ->first();

        if (!$instructor) {
            return response()->json([
                'success' => false,
                'message' => 'Instructor not found or does not belong to your college.'
            ]);
        }

        // Verify the offering belongs to this college
        $offering = \App\offerings_infos_table::where('id', $offeringId)->first();
        if ($offering) {
            $curriculum = curriculum::where('id', $offering->curriculum_id)
                ->where('college_code', $collegeCode)
                ->first();
            if (!$curriculum) {
                $offering = null;
            }
        }

        if (!$offering) {
            return response()->json([
                'success' => false,
                'message' => 'Offering not found or does not belong to your college.'
            ]);
        }

        // Check if the offering is already assigned to an instructor
        $existingSchedule = room_schedules::where('offering_id', $offeringId)
            ->where('is_active', 1)
            ->whereNotNull('instructor')
            ->first();

        if ($existingSchedule) {
            return response()->json([
                'success' => false,
                'message' => 'This course is already assigned to an instructor.'
            ]);
        }

        // Get the schedule for this offering
        $schedule = room_schedules::where('offering_id', $offeringId)
            ->where('is_active', 1)
            ->first();

        if (!$schedule) {
            return response()->json([
                'success' => false,
                'message' => 'No schedule found for this offering.'
            ]);
        }

        // Check for schedule conflicts
        $conflicts = $this->checkScheduleConflicts($instructorId, $schedule);
        if (count($conflicts) > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Schedule conflict detected: ' . implode(', ', $conflicts)
            ]);
        }

        // Assign the instructor to the schedule
        $schedule->instructor = $instructorId;
        $schedule->save();

        return response()->json([
            'success' => true,
            'message' => 'Faculty load added successfully.'
        ]);
    }

    /**
     * Remove faculty load
     */
    public function removeFacultyLoad(Request $request)
    {
        if ($request->has('schedule_id')) {
            // Remove by schedule ID
            $scheduleId = $request->input('schedule_id');
            $schedule = room_schedules::find($scheduleId);

            if (!$schedule) {
                return response()->json([
                    'success' => false,
                    'message' => 'Schedule not found.'
                ]);
            }

            $offering_id = $schedule->offering_id;
            $instructor = $schedule->instructor;

            // Remove instructor from all schedules with the same offering_id
            $schedules = room_schedules::where('instructor', $instructor)
                ->where('offering_id', $offering_id)
                ->get();
        } else {
            // Remove by instructor and offering_id
            $instructor = $request->input('instructor');
            $offering_id = $request->input('offering_id');

            // Get schedules
            $schedules = room_schedules::where('instructor', $instructor)
                ->where('offering_id', $offering_id)
                ->get();
        }

        if ($schedules->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'No schedules found.'
            ]);
        }

        // Remove instructor from schedules
        foreach ($schedules as $schedule) {
            $schedule->instructor = null;
            $schedule->is_loaded = 0;
            $schedule->save();
        }

        return response()->json([
            'success' => true,
            'message' => 'Faculty load removed successfully.'
        ]);
    }

    /**
     * Original remove faculty load method (for backward compatibility)
     */
    public function removeFacultyLoadOriginal(Request $request)
    {
        // Get schedule ID from request
        $scheduleId = $request->input('schedule_id');

        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Get the schedule
        $schedule = room_schedules::where('id', $scheduleId)
            ->where('is_active', 1)
            ->first();

        if (!$schedule) {
            return response()->json([
                'success' => false,
                'message' => 'Schedule not found.'
            ]);
        }

        // Verify the instructor belongs to this college
        $instructor = User::where('id', $schedule->instructor)
            ->where('accesslevel', 1)
            ->where(function ($query) use ($collegeCode) {
                $query->where('college_code', $collegeCode)
                    ->orWhereHas('instructorInfo', function ($q) use ($collegeCode) {
                        $q->where('college', $collegeCode);
                    });
            })
            ->first();

        if (!$instructor) {
            return response()->json([
                'success' => false,
                'message' => 'Instructor not found or does not belong to your college.'
            ]);
        }

        // Remove the instructor from the schedule
        $schedule->instructor = null;
        $schedule->save();

        return response()->json([
            'success' => true,
            'message' => 'Faculty load removed successfully.'
        ]);
    }

    /**
     * Search courses
     */
    public function searchCourses(Request $request)
    {
        // Get search term and level from request
        $value = $request->input('value');
        $level = $request->input('level');

        // Search for courses
        $curriculum = curriculum::where('course_code', 'like', "%$value%")->get();

        return view('collegeadmin.faculty_loading.ajax.search_courses', compact('curriculum', 'level'));
    }

    /**
     * Original search courses method (for backward compatibility)
     */
    public function searchCoursesOriginal(Request $request)
    {
        // Get search term and instructor ID from request
        $search = $request->input('search');
        $instructorId = $request->input('instructor_id');

        // Get the college code associated with the logged-in college admin
        $collegeCode = Auth::user()->college_code;

        // Search for offerings
        $offerings = \App\offerings_infos_table::where('is_active', 1)
            ->whereNotIn('id', function ($query) {
                $query->select('offering_id')
                    ->from('room_schedules')
                    ->where('is_active', 1)
                    ->whereNotNull('instructor');
            })
            ->with([
                'curriculum' => function ($query) use ($collegeCode) {
                    $query->where('college_code', $collegeCode);
                }
            ])
            ->get()
            ->filter(function ($offering) use ($search) {
                if (!$offering->curriculum) {
                    return false;
                }

                $curriculum = $offering->curriculum;
                $course_code = $curriculum->course_code ?: '';
                $course_name = $curriculum->course_name ?: '';

                // If course_name is empty or generic, try to get from subject relationship
                if (empty($curriculum->course_name) || strpos($curriculum->course_name, 'Course:') === 0) {
                    $subject = $curriculum->subject;
                    if ($subject) {
                        $course_code = $subject->subject_code;
                        $course_name = $subject->subject_name;
                    }
                }

                return (stripos($course_code, $search) !== false || stripos($course_name, $search) !== false);
            });

        // Add course_code and course_name to each offering
        foreach ($offerings as $offering) {
            $curriculum = $offering->curriculum;
            $offering->course_code = $curriculum->course_code ?: 'Unknown';
            $offering->course_name = $curriculum->course_name ?: 'Unknown';

            // If course_name is empty or generic, try to get from subject relationship
            if (
                empty($curriculum->course_name) || $curriculum->course_name == 'Unknown' ||
                strpos($curriculum->course_name, 'Course:') === 0
            ) {
                $subject = $curriculum->subject;
                if ($subject) {
                    $offering->course_code = $subject->subject_code;
                    $offering->course_name = $subject->subject_name;
                }
            }
        }

        return view('collegeadmin.ajax.search_courses', compact('offerings', 'instructorId'));
    }

    /**
     * Get units loaded for a course
     */
    public function get_units_loaded(Request $request)
    {
        // Get parameters from request
        $instructor = $request->input('instructor');
        $offering_id = $request->input('offering_id');
        $level = $request->input('level');

        // Get instructor type
        $type = \App\instructors_infos::where('instructor_id', $instructor)->first()->employee_type;

        // Get maximum units
        $units = \App\UnitsLoad::where('instructor_id', $instructor)->first()->units;

        // Get current schedules
        $tabular_schedules = room_schedules::distinct()
            ->where('is_active', 1)
            ->where('instructor', $instructor)
            ->get(['offering_id']);

        return view('collegeadmin.faculty_loading.ajax.get_units_loaded', compact('instructor', 'tabular_schedules', 'type', 'units', 'offering_id', 'level'));
    }

    /**
     * Override add faculty load
     */
    public function override_add(Request $request)
    {
        // Get parameters from request
        $instructor = $request->input('instructor');
        $offering_id = $request->input('offering_id');
        $override = $request->input('override');

        if ($override == 1) {
            // Check for schedule conflicts
            $schedules = room_schedules::where('offering_id', $offering_id)->get();

            foreach ($schedules as $schedule) {
                $conflicts = room_schedules::where('instructor', $instructor)
                    ->where('day', $schedule->day)
                    ->where(function ($query) use ($schedule) {
                        $query->whereBetween('time_starts', [$schedule->time_starts, $schedule->time_end])
                            ->orWhereBetween('time_end', [$schedule->time_starts, $schedule->time_end])
                            ->orWhere(function ($q) use ($schedule) {
                                $q->where('time_starts', '<=', $schedule->time_starts)
                                    ->where('time_end', '>=', $schedule->time_end);
                            });
                    })
                    ->get();

                if ($conflicts->count() > 0) {
                    abort(500);
                }
            }

            // Assign the instructor to the schedules
            foreach ($schedules as $schedule) {
                $schedule->instructor = $instructor;
                $schedule->is_loaded = 1;
                $schedule->save();
            }
        }
    }

    /**
     * Check for schedule conflicts
     */
    private function checkScheduleConflicts($instructorId, $newSchedule)
    {
        $conflicts = [];

        // Get existing schedules for this instructor
        $existingSchedules = room_schedules::where('instructor', $instructorId)
            ->where('is_active', 1)
            ->get();

        foreach ($existingSchedules as $existing) {
            // Check if schedules are on the same day
            $existingDays = str_split($existing->sched_day);
            $newDays = str_split($newSchedule->sched_day);

            $commonDays = array_intersect($existingDays, $newDays);

            if (count($commonDays) > 0) {
                // Check if schedules overlap in time
                $existingStart = strtotime($existing->sched_from);
                $existingEnd = strtotime($existing->sched_to);
                $newStart = strtotime($newSchedule->sched_from);
                $newEnd = strtotime($newSchedule->sched_to);

                if (
                    ($newStart >= $existingStart && $newStart < $existingEnd) ||
                    ($newEnd > $existingStart && $newEnd <= $existingEnd) ||
                    ($newStart <= $existingStart && $newEnd >= $existingEnd)
                ) {
                    // Get course code for the conflict
                    $offering = \App\offerings_infos_table::where('id', $existing->offering_id)->first();
                    $course_code = 'Unknown Course';

                    if ($offering) {
                        $curriculum = curriculum::find($offering->curriculum_id);
                        if ($curriculum) {
                            $course_code = $curriculum->course_code ?: 'Unknown Course';

                            // If course_code is empty, try to get from subject relationship
                            if (empty($curriculum->course_code)) {
                                $subject = $curriculum->subject;
                                if ($subject) {
                                    $course_code = $subject->subject_code;
                                }
                            }
                        }
                    }

                    $conflicts[] = $course_code;
                }
            }
        }

        return $conflicts;
    }
}
