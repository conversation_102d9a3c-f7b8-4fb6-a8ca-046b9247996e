<?php $__env->startSection('main-content'); ?>
<section class="content-header">
    <h1><i class="fa fa-dashboard"></i>
        Dashboard
        <small>Overview & Analytics</small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="<?php echo e(url('/')); ?>"><i class="fa fa-home"></i> Home</a></li>
        <li class="active">Dashboard</li>
    </ol>
</section>

<section class="content">
    <!-- Stats Boxes -->
    <div class="row">
        <div class="col-lg-3 col-xs-6">
          <!-- small box -->
          <div class="small-box bg-aqua">
            <div class="inner">
                <h3><?php echo e($totalRooms); ?></h3>
              <p>Total Rooms</p>
            </div>
            <div class="icon">
              <i class="fa fa-building"></i>
            </div>
          </div>
        </div>

        <div class="col-lg-3 col-xs-6">
          <!-- small box -->
          <div class="small-box bg-green">
            <div class="inner">
                <h3><?php echo e($totalPrograms); ?></h3>
              <p>Total Programs</p>
            </div>
            <div class="icon">
              <i class="fa fa-graduation-cap"></i>
            </div>
          </div>
        </div>

        <div class="col-lg-3 col-xs-6">
          <!-- small box -->
          <div class="small-box bg-yellow">
            <div class="inner">
                <h3><?php echo e($totalInstructors); ?></h3>
              <p>Total Instructors</p>
            </div>
            <div class="icon">
              <i class="fa fa-users"></i>
            </div>
          </div>
        </div>

        <div class="col-lg-3 col-xs-6">
          <!-- small box -->
          <div class="small-box bg-red">
            <div class="inner">
                <h3><?php echo e($totalSections); ?></h3>
              <p>Total Sections</p>
            </div>
            <div class="icon">
              <i class="fa fa-list-alt"></i>
            </div>
          </div>
        </div>
    </div>

    <!-- Quick Actions Panel -->
    <div class="row">
        <div class="col-md-12">
            <div class="box box-default">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-bolt"></i> Quick Actions</h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                    </div>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="<?php echo e(url('/superadmin/course_scheduling')); ?>" class="btn btn-app btn-block">
                                <i class="fa fa-calendar-plus-o"></i> Schedule Course
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo e(url('/superadmin/faculty_loading')); ?>" class="btn btn-app btn-block">
                                <i class="fa fa-users"></i> Faculty Loading
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo e(url('/superadmin/room_management')); ?>" class="btn btn-app btn-block">
                                <i class="fa fa-building"></i> Room Management
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo e(url('/superadmin/section_management')); ?>" class="btn btn-app btn-block">
                                <i class="fa fa-list-alt"></i> Section Management
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Class Schedule Overview -->
    <div class="row">
        <div class="col-md-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-calendar"></i> Class Schedule Overview</h3>
                    <div class="box-tools pull-right">
                        <div class="btn-group" style="margin-right: 10px;">
                            <button type="button" class="btn btn-sm btn-default" id="view-day">Day</button>
                            <button type="button" class="btn btn-sm btn-primary" id="view-week">Week</button>
                            <button type="button" class="btn btn-sm btn-default" id="view-month">Month</button>
                        </div>
                        <a href="<?php echo e(url('/superadmin/dashboard/export/schedule')); ?>" id="export-schedule" class="btn btn-sm btn-success" target="_blank">
                            <i class="fa fa-file-pdf-o"></i> Export PDF
                        </a>
                    </div>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="filter-container" style="margin-bottom: 15px;">
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>Department</label>
                                            <select class="form-control" id="filter-department">
                                                <option value="">All Departments</option>
                                                <?php $__currentLoopData = $colleges; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $college): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($college->college_code); ?>"><?php echo e($college->college_name); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>Instructor</label>
                                            <select class="form-control" id="filter-instructor">
                                                <option value="">All Instructors</option>
                                                <?php $__currentLoopData = $instructors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $instructor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($instructor->id); ?>"><?php echo e($instructor->lastname); ?>, <?php echo e($instructor->name); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>Room</label>
                                            <select class="form-control" id="filter-room">
                                                <option value="">All Rooms</option>
                                                <?php $__currentLoopData = $rooms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $room): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($room->room); ?>"><?php echo e($room->room); ?> (<?php echo e($room->building); ?>)</option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>Section</label>
                                            <select class="form-control" id="filter-section">
                                                <option value="">All Sections</option>
                                                <?php $__currentLoopData = $sections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($section->section_name); ?>"><?php echo e($section->section_name); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label>&nbsp;</label>
                                            <button type="button" class="btn btn-primary form-control" id="apply-filters">
                                                <i class="fa fa-search"></i> Apply Filters
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="legend-container">
                                            <span class="legend-item"><i class="fa fa-square" style="color: #28a745;"></i> Ongoing</span>
                                            <span class="legend-item"><i class="fa fa-square" style="color: #007bff;"></i> Upcoming</span>
                                            <span class="legend-item"><i class="fa fa-square" style="color: #6c757d;"></i> Completed</span>
                                            <span class="legend-item"><i class="fa fa-square" style="color: #dc3545;"></i> Canceled</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="calendar"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Room & Resource Monitoring -->
    <div class="row">
        <div class="col-md-12">
            <div class="box box-success">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-building"></i> Room & Resource Monitoring</h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                    </div>
                </div>
                <div class="box-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Date</label>
                                <div class="input-group">
                                    <div class="input-group-addon">
                                        <i class="fa fa-calendar"></i>
                                    </div>
                                    <input type="text" class="form-control" id="room-date-picker" value="<?php echo e(date('m/d/Y')); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Filter by Department</label>
                                <select class="form-control" id="room-department-filter">
                                    <option value="">All Departments</option>
                                    <?php $__currentLoopData = $colleges; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $college): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($college->college_code); ?>"><?php echo e($college->college_name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="button" class="btn btn-primary form-control" id="check-availability">
                                    <i class="fa fa-refresh"></i> Check Availability
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info" id="conflict-notification" style="display: none;">
                                <h4><i class="icon fa fa-warning"></i> Scheduling Conflicts Detected!</h4>
                                <p>There are scheduling conflicts in the system. Please review the rooms marked in red below.</p>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="room-availability-table">
                            <thead>
                                <tr>
                                    <th>Room</th>
                                    <th>Building</th>
                                    <th>Department</th>
                                    <th>Status</th>
                                    <th>Conflicts</th>
                                    <th>Schedule</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Room data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics and Reports -->
    <div class="row">
        <!-- Room Utilization -->
        <div class="col-md-6">
            <div class="box box-danger">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-pie-chart"></i> Room Utilization Rate</h3>
                    <div class="box-tools pull-right">
                        <a href="<?php echo e(url('/superadmin/dashboard/export/room_utilization')); ?>" class="btn btn-sm btn-success" target="_blank" style="margin-right: 5px;">
                            <i class="fa fa-file-pdf-o"></i> Export
                        </a>
                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                    </div>
                </div>
                <div class="box-body">
                    <canvas id="roomUtilizationChart" height="250"></canvas>
                </div>
            </div>
        </div>

        <!-- Instructor Load -->
        <div class="col-md-6">
            <div class="box box-warning">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-bar-chart"></i> Instructor Load (Hours/Week)</h3>
                    <div class="box-tools pull-right">
                        <a href="<?php echo e(url('/superadmin/dashboard/export/instructor_load')); ?>" class="btn btn-sm btn-success" target="_blank" style="margin-right: 5px;">
                            <i class="fa fa-file-pdf-o"></i> Export
                        </a>
                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                    </div>
                </div>
                <div class="box-body">
                    <canvas id="instructorLoadChart" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Enrollment Stats -->
        <div class="col-md-6">
            <div class="box box-info">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-users"></i> Enrollment vs. Capacity</h3>
                    <div class="box-tools pull-right">
                        <a href="<?php echo e(url('/superadmin/dashboard/export/enrollment_stats')); ?>" class="btn btn-sm btn-success" target="_blank" style="margin-right: 5px;">
                            <i class="fa fa-file-pdf-o"></i> Export
                        </a>
                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                    </div>
                </div>
                <div class="box-body">
                    <canvas id="enrollmentChart" height="250"></canvas>
                </div>
            </div>
        </div>

        <!-- Popular Time Slots -->
        <div class="col-md-6">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fa fa-clock-o"></i> Most Requested Time Slots</h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
                    </div>
                </div>
                <div class="box-body">
                    <canvas id="timeSlotChart" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('footer-script'); ?>
<link rel="stylesheet" href="<?php echo e(asset('plugins/fullcalendar/fullcalendar.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset('plugins/daterangepicker/daterangepicker.css')); ?>">
<script src="<?php echo e(asset('plugins/moment/moment.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/fullcalendar/fullcalendar.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/daterangepicker/daterangepicker.js')); ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@2.9.4/dist/Chart.min.js"></script>

<style>
    .legend-container {
        margin-top: 10px;
    }
    .legend-item {
        margin-right: 15px;
        font-size: 12px;
    }
    .btn-app {
        height: 80px;
        padding: 15px 5px;
        font-size: 14px;
    }
    .btn-app i {
        font-size: 24px;
        margin-bottom: 10px;
    }
    .date-range-container {
        margin-bottom: 15px;
    }
    .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 10px;
        padding: 3px 5px;
    }
    @media (max-width: 767px) {
        .box-tools .btn-group {
            margin-bottom: 10px;
        }
        .filter-container .form-group {
            margin-bottom: 15px;
        }
    }
</style>

<script>
    $(document).ready(function() {
        // Initialize calendar
        var calendar = $('#calendar').fullCalendar({
            header: false,
            firstDay: 1,
            columnFormat: 'ddd',
            defaultView: 'agendaWeek',
            hiddenDays: [0],
            minTime: '07:00:00',
            maxTime: '22:00:00',
            allDaySlot: false,
            height: 500,
            events: function(start, end, timezone, callback) {
                loadCalendarData(callback);
            },
            eventRender: function(event, element) {
                element.find('div.fc-title').html(element.find('div.fc-title').text());
            }
        });

        // View buttons
        $('#view-day').click(function() {
            $(this).addClass('btn-primary').removeClass('btn-default');
            $('#view-week, #view-month').addClass('btn-default').removeClass('btn-primary');
            calendar.fullCalendar('changeView', 'agendaDay');
        });

        $('#view-week').click(function() {
            $(this).addClass('btn-primary').removeClass('btn-default');
            $('#view-day, #view-month').addClass('btn-default').removeClass('btn-primary');
            calendar.fullCalendar('changeView', 'agendaWeek');
        });

        $('#view-month').click(function() {
            $(this).addClass('btn-primary').removeClass('btn-default');
            $('#view-day, #view-week').addClass('btn-default').removeClass('btn-primary');
            calendar.fullCalendar('changeView', 'month');
        });

        // Apply filters
        $('#apply-filters').click(function() {
            calendar.fullCalendar('refetchEvents');
        });

        // Initialize date picker
        $('#room-date-picker').daterangepicker({
            singleDatePicker: true,
            showDropdowns: true,
            locale: {
                format: 'MM/DD/YYYY'
            }
        });

        // Check room availability
        $('#check-availability').click(function() {
            loadRoomAvailability();
        });

        // Add date range picker for analytics
        $('<div class="row date-range-container"><div class="col-md-4"><div class="form-group"><label>Date Range for Analytics</label><div class="input-group"><div class="input-group-addon"><i class="fa fa-calendar"></i></div><input type="text" class="form-control" id="analytics-date-range"></div></div></div><div class="col-md-2"><div class="form-group"><label>&nbsp;</label><button type="button" class="btn btn-primary form-control" id="refresh-analytics"><i class="fa fa-refresh"></i> Refresh</button></div></div></div>')
            .insertBefore('#roomUtilizationChart').parent();

        // Initialize analytics date range picker
        $('#analytics-date-range').daterangepicker({
            startDate: moment().subtract(30, 'days'),
            endDate: moment(),
            ranges: {
               'Today': [moment(), moment()],
               'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
               'Last 7 Days': [moment().subtract(6, 'days'), moment()],
               'Last 30 Days': [moment().subtract(29, 'days'), moment()],
               'This Month': [moment().startOf('month'), moment().endOf('month')],
               'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
        });

        // Refresh analytics on date range change
        $('#refresh-analytics').click(function() {
            loadAnalyticsData();
        });

        // Load initial data
        loadRoomAvailability();
        loadAnalyticsData();

        // Check for conflicts and show notification
        function checkForConflicts() {
            var hasConflicts = false;
            $('#room-availability-table tbody tr').each(function() {
                if ($(this).hasClass('danger')) {
                    hasConflicts = true;
                    return false; // Break the loop
                }
            });

            if (hasConflicts) {
                $('#conflict-notification').slideDown();
            } else {
                $('#conflict-notification').slideUp();
            }
        }

        // Function to load calendar data with filters
        function loadCalendarData(callback) {
            var department = $('#filter-department').val();
            var instructor = $('#filter-instructor').val();
            var room = $('#filter-room').val();
            var section = $('#filter-section').val();
            var view = calendar.fullCalendar('getView').name;

            $.ajax({
                url: '/ajax/superadmin/dashboard/schedule_data',
                data: {
                    department: department,
                    instructor: instructor,
                    room: room,
                    section: section,
                    view: view
                },
                success: function(response) {
                    callback(response);
                }
            });
        }

        // Function to load room availability
        function loadRoomAvailability() {
            var date = $('#room-date-picker').val();
            var department = $('#room-department-filter').val();

            $.ajax({
                url: '/ajax/superadmin/dashboard/room_availability',
                data: {
                    date: moment(date, 'MM/DD/YYYY').format('YYYY-MM-DD'),
                    department: department
                },
                success: function(response) {
                    var tableBody = $('#room-availability-table tbody');
                    tableBody.empty();

                    $.each(response, function(index, room) {
                        var statusClass = room.is_available ? 'success' : 'warning';
                        var statusText = room.is_available ? 'Available' : 'Occupied';
                        var conflictCount = room.conflicts.length;
                        var conflictClass = conflictCount > 0 ? 'danger' : '';
                        var conflictText = conflictCount > 0 ? conflictCount + ' conflicts detected' : 'No conflicts';

                        var scheduleText = '';
                        if (room.time_slots.length > 0) {
                            $.each(room.time_slots, function(i, slot) {
                                scheduleText += moment(slot.start, 'HH:mm:ss').format('h:mm A') + ' - ' +
                                               moment(slot.end, 'HH:mm:ss').format('h:mm A') + '<br>';
                            });
                        } else {
                            scheduleText = 'No schedules';
                        }

                        var row = '<tr class="' + conflictClass + '">' +
                                  '<td>' + room.room + '</td>' +
                                  '<td>' + room.building + '</td>' +
                                  '<td>' + (room.college_code || 'N/A') + '</td>' +
                                  '<td><span class="label label-' + statusClass + '">' + statusText + '</span></td>' +
                                  '<td>' + conflictText + '</td>' +
                                  '<td>' + scheduleText + '</td>' +
                                  '</tr>';

                        tableBody.append(row);
                    });

                    // Check for conflicts after loading data
                    checkForConflicts();

                    // Update export URL with current filters
                    var exportUrl = '/superadmin/dashboard/export/schedule?';
                    if (department) exportUrl += 'department=' + department + '&';
                    $('#export-schedule').attr('href', exportUrl);
                }
            });
        }

        // Function to load analytics data
        function loadAnalyticsData() {
            // Get date range
            var dateRange = $('#analytics-date-range').val();
            var startDate = moment(dateRange.split(' - ')[0], 'MM/DD/YYYY').format('YYYY-MM-DD');
            var endDate = moment(dateRange.split(' - ')[1], 'MM/DD/YYYY').format('YYYY-MM-DD');

            // Room Utilization Chart
            $.ajax({
                url: '/ajax/superadmin/dashboard/analytics_data',
                data: {
                    type: 'room_utilization',
                    start_date: startDate,
                    end_date: endDate
                },
                success: function(response) {
                    var labels = [];
                    var data = [];
                    var backgroundColors = [];

                    $.each(response, function(index, room) {
                        labels.push(room.room + ' (' + room.building + ')');
                        data.push(room.utilization_rate);

                        // Generate random colors
                        var r = Math.floor(Math.random() * 255);
                        var g = Math.floor(Math.random() * 255);
                        var b = Math.floor(Math.random() * 255);
                        backgroundColors.push('rgba(' + r + ',' + g + ',' + b + ',0.7)');
                    });

                    var ctx = document.getElementById('roomUtilizationChart').getContext('2d');
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: 'Utilization Rate (%)',
                                data: data,
                                backgroundColor: backgroundColors
                            }]
                        },
                        options: {
                            scales: {
                                yAxes: [{
                                    ticks: {
                                        beginAtZero: true,
                                        max: 100
                                    }
                                }]
                            }
                        }
                    });
                }
            });

            // Instructor Load Chart
            $.ajax({
                url: '/ajax/superadmin/dashboard/analytics_data',
                data: {
                    type: 'instructor_load',
                    start_date: startDate,
                    end_date: endDate
                },
                success: function(response) {
                    var labels = [];
                    var data = [];

                    $.each(response, function(index, instructor) {
                        labels.push(instructor.instructor_name);
                        data.push(instructor.total_hours);
                    });

                    var ctx = document.getElementById('instructorLoadChart').getContext('2d');
                    new Chart(ctx, {
                        type: 'horizontalBar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: 'Teaching Hours/Week',
                                data: data,
                                backgroundColor: 'rgba(255, 193, 7, 0.7)'
                            }]
                        },
                        options: {
                            scales: {
                                xAxes: [{
                                    ticks: {
                                        beginAtZero: true
                                    }
                                }]
                            }
                        }
                    });
                }
            });

            // Enrollment Chart
            $.ajax({
                url: '/ajax/superadmin/dashboard/analytics_data',
                data: {
                    type: 'enrollment_stats',
                    start_date: startDate,
                    end_date: endDate
                },
                success: function(response) {
                    var labels = [];
                    var enrollmentData = [];
                    var capacityData = [];

                    $.each(response, function(index, section) {
                        labels.push(section.section);
                        enrollmentData.push(section.enrollment);
                        capacityData.push(section.capacity);
                    });

                    var ctx = document.getElementById('enrollmentChart').getContext('2d');
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [
                                {
                                    label: 'Enrollment',
                                    data: enrollmentData,
                                    backgroundColor: 'rgba(23, 162, 184, 0.7)'
                                },
                                {
                                    label: 'Capacity',
                                    data: capacityData,
                                    backgroundColor: 'rgba(108, 117, 125, 0.7)'
                                }
                            ]
                        },
                        options: {
                            scales: {
                                yAxes: [{
                                    ticks: {
                                        beginAtZero: true
                                    }
                                }]
                            }
                        }
                    });
                }
            });

            // Time Slot Chart
            $.ajax({
                url: '/ajax/superadmin/dashboard/analytics_data',
                data: {
                    type: 'popular_time_slots',
                    start_date: startDate,
                    end_date: endDate
                },
                success: function(response) {
                    // Time slots chart
                    var timeSlotLabels = [];
                    var timeSlotData = [];

                    $.each(response.time_slots, function(index, slot) {
                        // Format time slot for display
                        var times = slot.time_slot.split('-');
                        var formattedSlot = moment(times[0], 'HH:mm:ss').format('h:mm A') + ' - ' +
                                           moment(times[1], 'HH:mm:ss').format('h:mm A');

                        timeSlotLabels.push(formattedSlot);
                        timeSlotData.push(slot.count);
                    });

                    var timeSlotCtx = document.getElementById('timeSlotChart').getContext('2d');
                    new Chart(timeSlotCtx, {
                        type: 'pie',
                        data: {
                            labels: timeSlotLabels,
                            datasets: [{
                                data: timeSlotData,
                                backgroundColor: [
                                    'rgba(0, 123, 255, 0.7)',
                                    'rgba(40, 167, 69, 0.7)',
                                    'rgba(255, 193, 7, 0.7)',
                                    'rgba(220, 53, 69, 0.7)',
                                    'rgba(108, 117, 125, 0.7)'
                                ]
                            }]
                        }
                    });

                    // Add day distribution chart
                    if (response.days && response.days.length > 0) {
                        // Create a new canvas for day distribution
                        var dayChartContainer = $('<div class="col-md-6"><div class="box box-success"><div class="box-header with-border"><h3 class="box-title"><i class="fa fa-calendar-check-o"></i> Most Popular Days</h3><div class="box-tools pull-right"><button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button></div></div><div class="box-body"><canvas id="dayDistributionChart" height="250"></canvas></div></div></div>');

                        // Append after the time slot chart's parent column
                        $('#timeSlotChart').closest('.col-md-6').after(dayChartContainer);

                        var dayLabels = [];
                        var dayData = [];

                        $.each(response.days, function(index, day) {
                            dayLabels.push(day.day);
                            dayData.push(day.count);
                        });

                        var dayCtx = document.getElementById('dayDistributionChart').getContext('2d');
                        new Chart(dayCtx, {
                            type: 'bar',
                            data: {
                                labels: dayLabels,
                                datasets: [{
                                    label: 'Number of Classes',
                                    data: dayData,
                                    backgroundColor: 'rgba(40, 167, 69, 0.7)'
                                }]
                            },
                            options: {
                                scales: {
                                    yAxes: [{
                                        ticks: {
                                            beginAtZero: true
                                        }
                                    }]
                                }
                            }
                        });
                    }
                }
            });
        }
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.superadmin', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>