<!-- Control Sidebar -->
<aside class="control-sidebar control-sidebar-dark">
    <!-- Create the tabs -->
    <ul class="nav nav-tabs nav-justified control-sidebar-tabs">
        <li class="active"><a href="#control-sidebar-home-tab" data-toggle="tab"><i class="fa fa-home"></i></a></li>
        <li><a href="#control-sidebar-settings-tab" data-toggle="tab"><i class="fa fa-gears"></i></a></li>
    </ul>
    <!-- Tab panes -->
    <div class="tab-content">
        <!-- Home tab content -->
        <div class="tab-pane active" id="control-sidebar-home-tab">
            <h3 class="control-sidebar-heading"><?php echo e(trans('adminlte_lang::message.recentactivity')); ?></h3>
            <ul class='control-sidebar-menu'>
                <li>
                    <a href='javascript::;'>
                        <i class="menu-icon fa fa-birthday-cake bg-red"></i>
                        <div class="menu-info">
                            <h4 class="control-sidebar-subheading"><?php echo e(trans('adminlte_lang::message.birthday')); ?></h4>
                            <p><?php echo e(trans('adminlte_lang::message.birthdaydate')); ?></p>
                        </div>
                    </a>
                </li>
            </ul><!-- /.control-sidebar-menu -->

            <h3 class="control-sidebar-heading"><?php echo e(trans('adminlte_lang::message.progress')); ?></h3>
            <ul class='control-sidebar-menu'>
                <li>
                    <a href='javascript::;'>
                        <h4 class="control-sidebar-subheading">
                            <?php echo e(trans('adminlte_lang::message.customtemplate')); ?>

                            <span class="label label-danger pull-right">70%</span>
                        </h4>
                        <div class="progress progress-xxs">
                            <div class="progress-bar progress-bar-danger" style="width: 70%"></div>
                        </div>
                    </a>
                </li>
            </ul><!-- /.control-sidebar-menu -->

        </div><!-- /.tab-pane -->
        <!-- Stats tab content -->
        <div class="tab-pane" id="control-sidebar-stats-tab"><?php echo e(trans('adminlte_lang::message.statstab')); ?></div><!-- /.tab-pane -->
        <!-- Settings tab content -->
        <div class="tab-pane" id="control-sidebar-settings-tab">
            <form method="post">
                <h3 class="control-sidebar-heading"><?php echo e(trans('adminlte_lang::message.generalset')); ?></h3>
                <div class="form-group">
                    <label class="control-sidebar-subheading">
                        <?php echo e(trans('adminlte_lang::message.reportpanel')); ?>

                        <input type="checkbox" class="pull-right" <?php echo e(trans('adminlte_lang::message.checked')); ?> />
                    </label>
                    <p>
                        <?php echo e(trans('adminlte_lang::message.informationsettings')); ?>

                    </p>
                </div><!-- /.form-group -->
            </form>
        </div><!-- /.tab-pane -->
    </div>
</aside><!-- /.control-sidebar

<!-- Add the sidebar's background. This div must be placed
       immediately after the control sidebar -->
<div class='control-sidebar-bg'></div>